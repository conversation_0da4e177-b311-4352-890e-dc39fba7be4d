import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/integrations/supabase/types';

// Use environment variables if available, otherwise use these default values
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://puckwihzpprgpgepusmz.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1Y2t3aWh6cHByZ3BnZXB1c216Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTI5MjM0ODMsImV4cCI6MjAyODQ5OTQ4M30.aPemcVPXKk-FvEe8Q5eJeA5CU4g_uZkHrwZOODLXl3g';

// Create Supabase client with security options
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: {
      // Use secure storage options
      getItem: (key) => {
        try {
          const item = sessionStorage.getItem(key);
          return item ? JSON.parse(item) : null;
        } catch {
          return null;
        }
      },
      setItem: (key, value) => {
        try {
          sessionStorage.setItem(key, JSON.stringify(value));
        } catch {
          console.error('Error storing auth session');
        }
      },
      removeItem: (key) => {
        try {
          sessionStorage.removeItem(key);
        } catch {
          console.error('Error removing auth session');
        }
      },
    },
  },
  global: {
    headers: {
      'X-Client-Info': 'nitebite-web',
    },
  },
});

// Types for orders and cart items
export type Order = {
  id: string;
  user_id: string;
  amount: number;
  items: CartItem[];
  phone_number: string;
  hostel_number: string;
  room_number: string;
  payment_method: 'cod' | 'qr';
  payment_status: 'pending' | 'paid';
  created_at: string;
  updated_at: string;
};

export type CartItem = {
  id: string;
  name: string;
  price: number;
  quantity: number;
};

export type UserProfile = {
  id: string;
  user_id: string;
  full_name: string;
  phone_number: string;
  email: string;
  created_at: string;
  updated_at: string;
};
