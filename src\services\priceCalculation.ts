import { CartItem } from '@/store/cartStore';
import { supabase } from '@/supabaseClient';

// Interface for price calculation response
export interface PriceCalculationResult {
  subtotal: number;
  deliveryFee: number;
  convenienceFee: number;
  appliedDiscount: number;
  total: number;
  freeDeliveryThreshold: number;
  remainingForFreeDelivery: number;
}

/**
 * Calculate prices on the server side to prevent client-side manipulation
 * @param items Cart items
 * @param couponDiscount Applied coupon discount
 * @returns Promise with calculation result
 */
// Set to true to force client-side calculation
const FORCE_CLIENT_CALCULATION = true;

export async function calculatePricesOnServer(
  items: CartItem[],
  couponDiscount: number
): Promise<PriceCalculationResult> {
  try {
    // Calculate prices on client side
    return calculatePricesFallback(items, couponDiscount);
  } catch (err) {
    console.error('Unexpected error in price calculation:', err);
    // Fallback to client-side calculation if anything fails
    return calculatePricesFallback(items, couponDiscount);
  }
}

/**
 * Fallback client-side price calculation (only used if server calculation fails)
 * @param items Cart items
 * @param couponDiscount Applied coupon discount
 * @returns Price calculation result
 */
function calculatePricesFallback(
  items: CartItem[],
  couponDiscount: number
): PriceCalculationResult {
  // Calculate subtotal
  const subtotal = items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);

  // Constants
  const freeDeliveryThreshold = 149;
  const remainingForFreeDelivery = subtotal < freeDeliveryThreshold
    ? freeDeliveryThreshold - subtotal
    : 0;

  // Calculate fees
  const deliveryFee = subtotal >= freeDeliveryThreshold ? 0 : (items.length > 0 ? 10 : 0);
  const convenienceFee = subtotal > 0 ? 6 : 0;

  // Apply discount only if subtotal is greater than 0
  const appliedDiscount = (subtotal > 0 && couponDiscount)
    ? Math.min(couponDiscount, subtotal)
    : 0;

  // Calculate total (ensure it's never negative)
  const totalBeforeDiscount = subtotal + deliveryFee + convenienceFee;
  const total = Math.max(0, totalBeforeDiscount - appliedDiscount);

  return {
    subtotal,
    deliveryFee,
    convenienceFee,
    appliedDiscount,
    total,
    freeDeliveryThreshold,
    remainingForFreeDelivery
  };
}