
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import { createContext, useContext, useEffect } from "react";
import { HelmetProvider } from 'react-helmet-async';
import { supabase } from "./supabaseClient";

// Import the getValidCSRFToken function directly to avoid path issues
const getValidCSRFToken = () => {
  // Generate a token if none exists
  let token = sessionStorage.getItem('csrf_token');
  if (!token) {
    // Generate a random token
    const array = new Uint8Array(16);
    window.crypto.getRandomValues(array);
    token = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    sessionStorage.setItem('csrf_token', token);
    localStorage.setItem('csrf_token_backup', token);
  }
  return token;
};

import Index from "./pages/Index";
import Products from "./pages/Products";
import Checkout from "./pages/Checkout";
import OrderDetails from "./pages/OrderDetails";
import NotFound from "./pages/NotFound";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import Account from "./pages/Account";
import BoxBuilder from "./pages/BoxBuilder";
import SnackBoxSelector from "./pages/SnackBoxSelector";

// Create Supabase Context
const SupabaseContext = createContext(supabase);
export const useSupabase = () => useContext(SupabaseContext);

const queryClient = new QueryClient();

const App = () => {
  // Initialize CSRF token on app load
  useEffect(() => {
    const token = getValidCSRFToken();
    console.log('CSRF Token initialized:', token.substring(0, 4) + '...');
  }, []);

  return (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <SupabaseContext.Provider value={supabase}>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AnimatePresence mode="wait">
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/products" element={<Products />} />
                <Route path="/checkout" element={<Checkout />} />
                <Route path="/order-details" element={<OrderDetails />} />
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/account" element={<Account />} />
                <Route path="/box-builder" element={<BoxBuilder />} />
                <Route path="/snack-boxes" element={<SnackBoxSelector />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </AnimatePresence>
          </BrowserRouter>
        </SupabaseContext.Provider>
      </TooltipProvider>
    </QueryClientProvider>
  </HelmetProvider>
);
};

export default App;
